using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// Modbus RTU 通信基础类
    /// 提供完整的 Modbus RTU 协议栈实现，支持标准的 Modbus RTU 功能码操作
    /// </summary>
    /// <remarks>
    /// 此类实现了 Modbus RTU 协议的核心功能，包括：
    /// - 读取保持寄存器 (功能码 03H)
    /// - 写入单个寄存器 (功能码 06H)
    /// - 读取线圈状态 (功能码 01H)
    /// - 写入单个线圈 (功能码 05H)
    /// - CRC-16 校验
    /// - 自动重试机制
    /// - 线程安全的通信控制
    ///
    /// 使用场景：
    /// - PEM电解槽测试系统中的设备通信
    /// - 工业自动化设备的数据采集和控制
    /// - 需要可靠串口通信的应用场景
    ///
    /// 注意事项：
    /// - 继承此类需要实现具体的设备通信逻辑
    /// - 所有通信操作都是线程安全的
    /// - 使用完毕后需要调用 Dispose() 释放资源
    /// - 支持自动重连和错误恢复机制
    /// </remarks>
    public abstract class ModbusRtuBase : IDisposable
    {
        /// <summary>
        /// 串口通信对象
        /// 用于与 Modbus RTU 设备进行串口通信
        /// </summary>
        /// <remarks>
        /// 配置参数：数据位8位，无奇偶校验，1个停止位
        /// 读写超时时间由构造函数参数指定
        /// </remarks>
        protected readonly SerialPort _serialPort;

        /// <summary>
        /// Modbus 从站地址
        /// 标识要通信的目标设备地址
        /// </summary>
        /// <remarks>
        /// 取值范围：1-247（0为广播地址，248-255为保留地址）
        /// 每个 Modbus 网络中的设备必须有唯一的从站地址
        /// </remarks>
        protected readonly byte _slaveAddress;

        /// <summary>
        /// 通信同步锁
        /// 确保同一时间只有一个通信操作在进行，保证线程安全
        /// </summary>
        /// <remarks>
        /// 使用信号量实现，最大并发数为1
        /// 防止多线程同时访问串口导致数据混乱
        /// </remarks>
        protected readonly SemaphoreSlim _communicationLock;

        /// <summary>
        /// 通信超时时间（毫秒）
        /// 单次通信操作的最大等待时间
        /// </summary>
        /// <remarks>
        /// 默认值：5000毫秒
        /// 包括发送请求和接收响应的总时间
        /// 超时后会触发重试机制
        /// </remarks>
        protected readonly int _timeout;

        /// <summary>
        /// 最大重试次数
        /// 通信失败时的最大重试次数
        /// </summary>
        /// <remarks>
        /// 默认值：3次
        /// 重试间隔会递增延迟（100ms * 重试次数）
        /// 所有重试失败后返回错误结果
        /// </remarks>
        protected readonly int _maxRetries;

        /// <summary>
        /// 资源释放标志
        /// 标识对象是否已被释放，防止重复释放
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 初始化 Modbus RTU 基类
        /// 配置串口通信参数和设备连接信息
        /// </summary>
        /// <param name="portName">串口名称（如 "COM1", "COM2" 等）</param>
        /// <param name="baudRate">波特率（常用值：9600, 19200, 38400, 115200）</param>
        /// <param name="slaveAddress">Modbus 从站地址（1-247）</param>
        /// <param name="timeout">通信超时时间（毫秒），默认5000ms</param>
        /// <param name="maxRetries">最大重试次数，默认3次</param>
        /// <exception cref="ArgumentException">当从站地址超出有效范围时抛出</exception>
        /// <exception cref="ArgumentNullException">当端口名称为空时抛出</exception>
        /// <remarks>
        /// 串口配置固定为：8数据位，无奇偶校验，1停止位
        /// 这是 Modbus RTU 协议的标准配置
        /// 构造函数不会打开串口连接，需要调用 ConnectAsync() 方法建立连接
        /// </remarks>
        protected ModbusRtuBase(
            string portName,
            int baudRate,
            byte slaveAddress,
            int timeout = 5000,
            int maxRetries = 3)
        {
            // 保存从站地址
            _slaveAddress = slaveAddress;
            // 保存超时时间
            _timeout = timeout;
            // 保存最大重试次数
            _maxRetries = maxRetries;
            // 初始化通信锁，确保线程安全
            _communicationLock = new SemaphoreSlim(1, 1);

            // 配置串口参数
            _serialPort = new SerialPort
            {
                PortName = portName,        // 串口名称
                BaudRate = baudRate,        // 波特率
                DataBits = 8,              // 数据位（Modbus RTU 标准）
                Parity = Parity.None,      // 无奇偶校验（Modbus RTU 标准）
                StopBits = StopBits.One,   // 1个停止位（Modbus RTU 标准）
                ReadTimeout = timeout,      // 读取超时时间
                WriteTimeout = timeout      // 写入超时时间
            };

            // 记录初始化日志
            App.AlarmService.Debug("Modbus RTU", $"初始化 Modbus RTU 基类 - 端口: {portName}, 波特率: {baudRate}, 从站地址: {slaveAddress}");
        }

        /// <summary>
        /// 异步连接到 Modbus RTU 设备
        /// 打开串口连接并初始化通信环境
        /// </summary>
        /// <returns>
        /// 连接结果：
        /// - true: 连接成功或已经连接
        /// - false: 连接失败
        /// </returns>
        /// <exception cref="UnauthorizedAccessException">串口被其他应用程序占用</exception>
        /// <exception cref="ArgumentException">串口名称无效</exception>
        /// <exception cref="InvalidOperationException">串口配置错误</exception>
        /// <remarks>
        /// 连接过程包括：
        /// 1. 检查串口是否已经打开
        /// 2. 打开串口连接
        /// 3. 清空输入输出缓冲区
        /// 4. 记录连接状态日志
        ///
        /// 线程安全：此方法是线程安全的
        /// 重复调用：如果已经连接，直接返回成功
        /// </remarks>
        public virtual async Task<bool> ConnectAsync()
        {
            try
            {
                // 检查串口是否已经打开
                if (_serialPort.IsOpen)
                {
                    App.AlarmService.Warning("Modbus RTU", "串口已经打开");
                    return true;
                }

                // 异步打开串口
                await Task.Run(() => _serialPort.Open());

                // 清空输入输出缓冲区，确保通信环境干净
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                App.AlarmService.Info("设备连接", $"Modbus RTU 连接成功 - 端口: {_serialPort.PortName}, 从站地址: {_slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"Modbus RTU 连接失败 - 端口: {_serialPort.PortName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步断开 Modbus RTU 设备连接
        /// 安全关闭串口连接
        /// </summary>
        /// <returns>
        /// 断开结果：
        /// - true: 断开成功或已经断开
        /// - false: 断开过程中发生错误
        /// </returns>
        /// <remarks>
        /// 断开过程包括：
        /// 1. 检查串口是否已经打开
        /// 2. 安全关闭串口连接
        /// 3. 记录断开状态日志
        ///
        /// 线程安全：此方法是线程安全的
        /// 重复调用：如果已经断开，直接返回成功
        /// 异常处理：即使关闭过程中出现异常，也会记录日志并返回失败状态
        /// </remarks>
        public virtual async Task<bool> DisconnectAsync()
        {
            try
            {
                // 检查串口是否打开，如果打开则关闭
                if (_serialPort.IsOpen)
                {
                    await Task.Run(() => _serialPort.Close());
                }

                App.AlarmService.Info("设备连接", $"Modbus RTU 断开连接 - 端口: {_serialPort.PortName}, 从站地址: {_slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"Modbus RTU 断开连接失败 - 端口: {_serialPort.PortName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 异步检查设备连接状态
        /// 快速检查串口是否处于打开状态
        /// </summary>
        /// <returns>
        /// 连接状态：
        /// - true: 设备已连接（串口已打开）
        /// - false: 设备未连接（串口未打开或对象已释放）
        /// </returns>
        /// <remarks>
        /// 此方法只检查串口的打开状态，不进行实际的通信测试
        /// 如果需要验证设备是否真正可通信，建议发送测试命令
        /// 执行速度快，适合频繁调用的场景
        /// </remarks>
        public virtual Task<bool> IsConnectedAsync()
        {
            return Task.FromResult(_serialPort?.IsOpen == true);
        }

        /// <summary>
        /// 异步读取保持寄存器数据 (Modbus 功能码 03H)
        /// 从指定地址开始读取连续的保持寄存器值
        /// </summary>
        /// <param name="startAddress">起始寄存器地址（0-65535）</param>
        /// <param name="quantity">要读取的寄存器数量（1-125）</param>
        /// <returns>
        /// 读取结果：
        /// - 成功：包含寄存器值的 ushort 数组
        /// - 失败：null
        /// </returns>
        /// <exception cref="ArgumentException">当寄存器数量超出有效范围时抛出</exception>
        /// <remarks>
        /// 保持寄存器特点：
        /// - 16位寄存器，可读可写
        /// - 常用于存储配置参数、设定值等
        /// - 单次最多读取125个寄存器（Modbus RTU 限制）
        ///
        /// 通信流程：
        /// 1. 构建读取请求帧
        /// 2. 发送请求并等待响应（支持重试）
        /// 3. 解析响应数据
        /// 4. 返回寄存器值数组
        ///
        /// 线程安全：此方法是线程安全的
        /// </remarks>
        protected async Task<ushort[]?> ReadHoldingRegistersAsync(ushort startAddress, ushort quantity)
        {
            // 验证寄存器数量参数
            if (quantity == 0 || quantity > 125)
            {
                throw new ArgumentException("寄存器数量必须在 1-125 之间", nameof(quantity));
            }

            // 构建读取保持寄存器请求帧
            var request = BuildReadHoldingRegistersRequest(startAddress, quantity);
            // 发送请求并获取响应（包含重试机制）
            var response = await SendRequestWithRetryAsync(request);

            // 检查响应是否有效
            if (response == null)
                return null;

            // 解析响应数据并返回寄存器值
            return ParseReadHoldingRegistersResponse(response, quantity);
        }

        /// <summary>
        /// 异步写入单个保持寄存器 (Modbus 功能码 06H)
        /// 向指定地址的寄存器写入16位数值
        /// </summary>
        /// <param name="address">目标寄存器地址（0-65535）</param>
        /// <param name="value">要写入的16位数值（0-65535）</param>
        /// <returns>
        /// 写入结果：
        /// - true: 写入成功
        /// - false: 写入失败
        /// </returns>
        /// <remarks>
        /// 写入过程：
        /// 1. 构建写入请求帧
        /// 2. 发送请求并等待响应（支持重试）
        /// 3. 验证响应是否与请求匹配
        ///
        /// 响应验证：
        /// - 检查从站地址和功能码
        /// - 验证返回的地址和数值是否与请求一致
        ///
        /// 线程安全：此方法是线程安全的
        /// 应用场景：设置设备参数、控制输出值等
        /// </remarks>
        protected async Task<bool> WriteSingleRegisterAsync(ushort address, ushort value)
        {
            // 构建写入单个寄存器请求帧
            var request = BuildWriteSingleRegisterRequest(address, value);
            // 发送请求并获取响应（包含重试机制）
            var response = await SendRequestWithRetryAsync(request);

            // 检查响应是否有效
            if (response == null)
                return false;

            // 验证响应数据是否正确
            return ValidateWriteSingleRegisterResponse(response, address, value);
        }

        /// <summary>
        /// 异步读取线圈状态 (Modbus 功能码 01H)
        /// 从指定地址开始读取连续的线圈状态
        /// </summary>
        /// <param name="startAddress">起始线圈地址（0-65535）</param>
        /// <param name="quantity">要读取的线圈数量（1-2000）</param>
        /// <returns>
        /// 读取结果：
        /// - 成功：包含线圈状态的 bool 数组（true=ON, false=OFF）
        /// - 失败：null
        /// </returns>
        /// <exception cref="ArgumentException">当线圈数量超出有效范围时抛出</exception>
        /// <remarks>
        /// 线圈特点：
        /// - 1位数字量，表示开关状态
        /// - 常用于读取开关输入、状态指示等
        /// - 单次最多读取2000个线圈（Modbus RTU 限制）
        ///
        /// 数据编码：
        /// - 每8个线圈打包成1个字节
        /// - 最低位对应最小地址的线圈
        /// - 不足8位的最后一个字节高位补0
        ///
        /// 线程安全：此方法是线程安全的
        /// </remarks>
        protected async Task<bool[]?> ReadCoilsAsync(ushort startAddress, ushort quantity)
        {
            // 验证线圈数量参数
            if (quantity == 0 || quantity > 2000)
            {
                throw new ArgumentException("线圈数量必须在 1-2000 之间", nameof(quantity));
            }

            // 构建读取线圈请求帧
            var request = BuildReadCoilsRequest(startAddress, quantity);
            // 发送请求并获取响应（包含重试机制）
            var response = await SendRequestWithRetryAsync(request);

            // 检查响应是否有效
            if (response == null)
                return null;

            // 解析响应数据并返回线圈状态
            return ParseReadCoilsResponse(response, quantity);
        }

        /// <summary>
        /// 异步写入单个线圈 (Modbus 功能码 05H)
        /// 向指定地址的线圈写入开关状态
        /// </summary>
        /// <param name="address">目标线圈地址（0-65535）</param>
        /// <param name="value">要写入的状态值（true=ON, false=OFF）</param>
        /// <returns>
        /// 写入结果：
        /// - true: 写入成功
        /// - false: 写入失败
        /// </returns>
        /// <remarks>
        /// 写入过程：
        /// 1. 构建写入请求帧
        /// 2. 发送请求并等待响应（支持重试）
        /// 3. 验证响应是否与请求匹配
        ///
        /// 数据编码：
        /// - true 编码为 0xFF00
        /// - false 编码为 0x0000
        ///
        /// 线程安全：此方法是线程安全的
        /// 应用场景：控制继电器、指示灯、阀门等开关设备
        /// </remarks>
        protected async Task<bool> WriteSingleCoilAsync(ushort address, bool value)
        {
            // 构建写入单个线圈请求帧
            var request = BuildWriteSingleCoilRequest(address, value);
            // 发送请求并获取响应（包含重试机制）
            var response = await SendRequestWithRetryAsync(request);

            // 检查响应是否有效
            if (response == null)
                return false;

            // 验证响应数据是否正确
            return ValidateWriteSingleCoilResponse(response, address, value);
        }

        /// <summary>
        /// 带重试机制的请求发送
        /// </summary>
        private async Task<byte[]?> SendRequestWithRetryAsync(byte[] request)
        {
            await _communicationLock.WaitAsync();
            
            try
            {
                for (int attempt = 1; attempt <= _maxRetries; attempt++)
                {
                    try
                    {
                        if (!_serialPort.IsOpen)
                        {
                            App.AlarmService.Warning("设备通信", "串口未打开，尝试重新连接");
                            if (!await ConnectAsync())
                            {
                                continue;
                            }
                        }

                        var response = await SendRequestAsync(request);
                        if (response != null)
                        {
                            if (attempt > 1)
                            {
                                App.AlarmService.Info("设备通信", $"第 {attempt} 次重试成功");
                            }
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("设备通信", $"第 {attempt} 次通信尝试失败: {ex.Message}");
                        
                        if (attempt < _maxRetries)
                        {
                            await Task.Delay(100 * attempt); // 递增延迟
                        }
                    }
                }

                App.AlarmService.Error("设备通信", $"通信失败，已重试 {_maxRetries} 次");
                return null;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 发送请求并接收响应
        /// </summary>
        private async Task<byte[]?> SendRequestAsync(byte[] request)
        {
            try
            {
                // 清空接收缓冲区
                _serialPort.DiscardInBuffer();

                // 发送请求
                await Task.Run(() => _serialPort.Write(request, 0, request.Length));
                
                App.AlarmService.Debug("Modbus RTU", $"发送请求: {BitConverter.ToString(request)}");

                // 等待响应
                await Task.Delay(20); // 帧间静默时间

                // 读取响应
                var response = await ReadResponseAsync();
                
                if (response != null)
                {
                    App.AlarmService.Debug("Modbus RTU", $"接收响应: {BitConverter.ToString(response)}");
                    
                    // 验证响应
                    if (ValidateResponse(response))
                    {
                        return response;
                    }
                    else
                    {
                        App.AlarmService.Warning("设备通信", "响应验证失败");
                    }
                }

                return null;
            }
            catch (TimeoutException)
            {
                App.AlarmService.Warning("设备通信", "通信超时");
                return null;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备通信", "通信异常", ex);
                return null;
            }
        }

        /// <summary>
        /// 读取响应数据
        /// </summary>
        private async Task<byte[]?> ReadResponseAsync()
        {
            var buffer = new List<byte>();
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < _timeout)
            {
                if (_serialPort.BytesToRead > 0)
                {
                    var data = new byte[_serialPort.BytesToRead];
                    var bytesRead = await Task.Run(() => _serialPort.Read(data, 0, data.Length));
                    buffer.AddRange(data.Take(bytesRead));

                    // 检查是否接收完整
                    if (buffer.Count >= 4) // 最小响应长度
                    {
                        // 等待一小段时间确保没有更多数据
                        await Task.Delay(10);
                        if (_serialPort.BytesToRead == 0)
                        {
                            return buffer.ToArray();
                        }
                    }
                }
                else
                {
                    await Task.Delay(1);
                }
            }

            return buffer.Count > 0 ? buffer.ToArray() : null;
        }

        #region CRC 校验

        /// <summary>
        /// 计算 CRC-16 校验码
        /// </summary>
        protected static ushort CalculateCrc16(byte[] data)
        {
            ushort crc = 0xFFFF;

            for (int i = 0; i < data.Length; i++)
            {
                crc ^= data[i];

                for (int j = 0; j < 8; j++)
                {
                    if ((crc & 0x0001) != 0)
                    {
                        crc = (ushort)((crc >> 1) ^ 0xA001);
                    }
                    else
                    {
                        crc >>= 1;
                    }
                }
            }

            return crc;
        }

        /// <summary>
        /// 验证 CRC 校验码
        /// </summary>
        protected static bool ValidateCrc16(byte[] data)
        {
            if (data.Length < 4)
                return false;

            var dataWithoutCrc = new byte[data.Length - 2];
            Array.Copy(data, 0, dataWithoutCrc, 0, data.Length - 2);

            var calculatedCrc = CalculateCrc16(dataWithoutCrc);
            var receivedCrc = (ushort)(data[data.Length - 2] | (data[data.Length - 1] << 8));

            return calculatedCrc == receivedCrc;
        }

        #endregion

        #region 帧构建方法

        /// <summary>
        /// 构建读取保持寄存器请求帧
        /// </summary>
        private byte[] BuildReadHoldingRegistersRequest(ushort startAddress, ushort quantity)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x03;                    // 功能码
            frame[2] = (byte)(startAddress >> 8); // 起始地址高字节
            frame[3] = (byte)(startAddress & 0xFF); // 起始地址低字节
            frame[4] = (byte)(quantity >> 8);    // 数量高字节
            frame[5] = (byte)(quantity & 0xFF);  // 数量低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建写入单个寄存器请求帧
        /// </summary>
        private byte[] BuildWriteSingleRegisterRequest(ushort address, ushort value)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x06;                    // 功能码
            frame[2] = (byte)(address >> 8);    // 寄存器地址高字节
            frame[3] = (byte)(address & 0xFF);  // 寄存器地址低字节
            frame[4] = (byte)(value >> 8);      // 数据高字节
            frame[5] = (byte)(value & 0xFF);    // 数据低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建读取线圈请求帧
        /// </summary>
        private byte[] BuildReadCoilsRequest(ushort startAddress, ushort quantity)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x01;                    // 功能码
            frame[2] = (byte)(startAddress >> 8); // 起始地址高字节
            frame[3] = (byte)(startAddress & 0xFF); // 起始地址低字节
            frame[4] = (byte)(quantity >> 8);    // 数量高字节
            frame[5] = (byte)(quantity & 0xFF);  // 数量低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建写入单个线圈请求帧
        /// </summary>
        private byte[] BuildWriteSingleCoilRequest(ushort address, bool value)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x05;                    // 功能码
            frame[2] = (byte)(address >> 8);    // 线圈地址高字节
            frame[3] = (byte)(address & 0xFF);  // 线圈地址低字节
            frame[4] = (byte)(value ? 0xFF : 0x00); // 数据高字节
            frame[5] = 0x00;                    // 数据低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        #endregion

        #region 响应解析方法

        /// <summary>
        /// 解析读取保持寄存器响应
        /// </summary>
        private ushort[]? ParseReadHoldingRegistersResponse(byte[] response, ushort expectedQuantity)
        {
            if (response.Length < 5)
                return null;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x03)
                return null;

            var byteCount = response[2];
            var expectedByteCount = expectedQuantity * 2;

            if (byteCount != expectedByteCount || response.Length != byteCount + 5)
                return null;

            var registers = new ushort[expectedQuantity];
            for (int i = 0; i < expectedQuantity; i++)
            {
                var highByte = response[3 + i * 2];
                var lowByte = response[4 + i * 2];
                registers[i] = (ushort)((highByte << 8) | lowByte);
            }

            return registers;
        }

        /// <summary>
        /// 验证写入单个寄存器响应
        /// </summary>
        private bool ValidateWriteSingleRegisterResponse(byte[] response, ushort address, ushort value)
        {
            if (response.Length != 8)
                return false;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x06)
                return false;

            // 检查地址和数据是否匹配
            var responseAddress = (ushort)((response[2] << 8) | response[3]);
            var responseValue = (ushort)((response[4] << 8) | response[5]);

            return responseAddress == address && responseValue == value;
        }

        /// <summary>
        /// 解析读取线圈响应
        /// </summary>
        private bool[]? ParseReadCoilsResponse(byte[] response, ushort expectedQuantity)
        {
            if (response.Length < 4)
                return null;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x01)
                return null;

            var byteCount = response[2];
            var expectedByteCount = (expectedQuantity + 7) / 8; // 向上取整

            if (byteCount != expectedByteCount || response.Length != byteCount + 5)
                return null;

            var coils = new bool[expectedQuantity];
            for (int i = 0; i < expectedQuantity; i++)
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                var dataByte = response[3 + byteIndex];
                coils[i] = (dataByte & (1 << bitIndex)) != 0;
            }

            return coils;
        }

        /// <summary>
        /// 验证写入单个线圈响应
        /// </summary>
        private bool ValidateWriteSingleCoilResponse(byte[] response, ushort address, bool value)
        {
            if (response.Length != 8)
                return false;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x05)
                return false;

            // 检查地址和数据是否匹配
            var responseAddress = (ushort)((response[2] << 8) | response[3]);
            var responseValue = response[4] == 0xFF;

            return responseAddress == address && responseValue == value;
        }

        /// <summary>
        /// 验证响应帧
        /// </summary>
        private bool ValidateResponse(byte[] response)
        {
            if (response.Length < 4)
            {
                App.AlarmService.Warning("设备通信", "响应帧长度不足");
                return false;
            }

            // 检查从站地址
            if (response[0] != _slaveAddress)
            {
                App.AlarmService.Warning("设备通信", $"从站地址不匹配，期望: {_slaveAddress}, 实际: {response[0]}");
                return false;
            }

            // 检查是否为错误响应
            if ((response[1] & 0x80) != 0)
            {
                var functionCode = (byte)(response[1] & 0x7F);
                var exceptionCode = response[2];
                App.AlarmService.Error("设备通信", $"设备返回异常 - 功能码: 0x{functionCode:X2}, 异常码: 0x{exceptionCode:X2}");
                return false;
            }

            // 验证 CRC
            if (!ValidateCrc16(response))
            {
                App.AlarmService.Warning("设备通信", "CRC 校验失败");
                return false;
            }

            return true;
        }

        #endregion

        #region IDisposable 实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        _serialPort?.Close();
                        _serialPort?.Dispose();
                        _communicationLock?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("Modbus RTU", "释放 Modbus RTU 资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
