using PEMTestSystem.Models.Devices;
using System;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// SCPI串口通信基类
    /// 提供基于串口的SCPI（Standard Commands for Programmable Instruments）协议通信功能，
    /// 包括设备连接管理、命令发送、查询响应处理等核心功能
    /// </summary>
    public abstract class ScpiSerialBase : DeviceBase
    {
        /// <summary>
        /// 串口通信对象，用于与SCPI设备进行串口通信
        /// </summary>
        protected readonly SerialPort _serialPort;

        /// <summary>
        /// 通信锁，确保串口通信的线程安全性，防止并发访问冲突
        /// </summary>
        protected readonly SemaphoreSlim _communicationLock;

        /// <summary>
        /// 通信超时时间（毫秒），用于串口读写操作的超时控制
        /// </summary>
        protected readonly int _timeout;

        /// <summary>
        /// 通信重试次数，当通信失败时的最大重试次数
        /// </summary>
        protected readonly int _retryCount;

        /// <summary>
        /// SCPI命令终止符，用于标识命令结束
        /// </summary>
        protected const string COMMAND_TERMINATOR = "\n";

        /// <summary>
        /// 默认通信超时时间（毫秒）
        /// </summary>
        protected const int DEFAULT_TIMEOUT = 3000;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        protected const int DEFAULT_RETRY_COUNT = 3;

        /// <summary>
        /// 响应延迟时间（毫秒），发送查询命令后等待设备响应的时间
        /// </summary>
        protected const int RESPONSE_DELAY = 50;

        /// <summary>
        /// 初始化SCPI串口通信基类
        /// </summary>
        /// <param name="portName">串口名称（如COM1、COM2等）</param>
        /// <param name="baudRate">波特率，必须与设备支持的波特率匹配</param>
        /// <param name="deviceId">设备唯一标识符</param>
        /// <param name="deviceType">设备类型枚举</param>
        /// <param name="deviceName">设备显示名称</param>
        /// <param name="model">设备型号</param>
        /// <param name="timeout">通信超时时间（毫秒），默认为3000毫秒</param>
        /// <param name="retryCount">通信失败时的重试次数，默认为3次</param>
        protected ScpiSerialBase(
            string portName,
            int baudRate,
            string deviceId,
            DeviceType deviceType,
            string deviceName,
            string model,
            int timeout = DEFAULT_TIMEOUT,
            int retryCount = DEFAULT_RETRY_COUNT)
            : base(deviceId, deviceType, deviceName, model)
        {
            _timeout = timeout;
            _retryCount = retryCount;
            _communicationLock = new SemaphoreSlim(1, 1);

            // 配置串口参数
            _serialPort = new SerialPort
            {
                PortName = portName,
                BaudRate = baudRate,
                DataBits = 8,
                Parity = Parity.None,
                StopBits = StopBits.One,
                Handshake = Handshake.None,
                ReadTimeout = timeout,
                WriteTimeout = timeout,
                Encoding = Encoding.ASCII
            };

            App.AlarmService.Debug("SCPI设备", $"SCPI设备 {DeviceId} 初始化完成，端口: {portName}, 波特率: {baudRate}");
        }

        /// <summary>
        /// 连接到SCPI设备
        /// 打开串口连接，清空缓冲区，并通过发送*IDN?命令测试通信是否正常
        /// </summary>
        /// <returns>连接成功返回true，失败返回false</returns>
        /// <exception cref="Exception">串口打开失败或通信测试失败时可能抛出异常</exception>
        protected override async Task<bool> ConnectInternalAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    App.AlarmService.Warning("SCPI设备", $"串口 {_serialPort.PortName} 已经打开");
                    return true;
                }

                _serialPort.Open();
                
                // 等待串口稳定
                await Task.Delay(100);

                // 清空缓冲区
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                // 测试通信 - 发送设备识别命令
                var idnResponse = await SendQueryAsync("*IDN?");
                if (string.IsNullOrEmpty(idnResponse))
                {
                    App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 通信测试失败");
                    _serialPort.Close();
                    return false;
                }

                App.AlarmService.Info("SCPI设备", $"设备 {DeviceId} 连接成功，设备信息: {idnResponse}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 连接失败", ex);
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                return false;
            }
        }

        /// <summary>
        /// 断开与SCPI设备的连接
        /// 关闭串口连接并等待端口完全关闭
        /// </summary>
        /// <returns>断开连接成功返回true，失败返回false</returns>
        /// <exception cref="Exception">关闭串口时可能抛出异常</exception>
        protected override async Task<bool> DisconnectInternalAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                
                await Task.Delay(100); // 等待端口完全关闭
                App.AlarmService.Info("SCPI设备", $"设备 {DeviceId} 断开连接");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 断开连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 发送SCPI命令（无返回值）
        /// 向设备发送SCPI命令，不等待响应。支持自动重试机制
        /// </summary>
        /// <param name="command">要发送的SCPI命令字符串，不需要包含终止符</param>
        /// <returns>命令发送成功返回true，失败返回false</returns>
        /// <exception cref="Exception">串口通信异常时可能抛出异常</exception>
        protected async Task<bool> SendCommandAsync(string command)
        {
            if (!IsConnected)
            {
                App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 未连接，无法发送命令: {command}");
                return false;
            }

            await _communicationLock.WaitAsync();
            try
            {
                for (int attempt = 1; attempt <= _retryCount; attempt++)
                {
                    try
                    {
                        // 清空输入缓冲区
                        _serialPort.DiscardInBuffer();

                        // 发送命令
                        var commandWithTerminator = command + COMMAND_TERMINATOR;
                        _serialPort.Write(commandWithTerminator);

                        App.AlarmService.Debug("SCPI设备", $"设备 {DeviceId} 发送命令: {command} (尝试 {attempt}/{_retryCount})");

                        LastCommunicationTime = DateTime.Now;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 发送命令失败 (尝试 {attempt}/{_retryCount}): {command} - {ex.Message}");

                        if (attempt < _retryCount)
                        {
                            await Task.Delay(100); // 重试前等待
                        }
                    }
                }

                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 发送命令最终失败: {command}");
                return false;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 发送SCPI查询命令并获取响应
        /// 向设备发送查询命令，等待并读取设备响应。支持自动重试机制和超时处理
        /// </summary>
        /// <param name="query">要发送的SCPI查询命令字符串，不需要包含终止符</param>
        /// <returns>设备响应字符串，通信失败或超时时返回null</returns>
        /// <exception cref="TimeoutException">读取响应超时时抛出</exception>
        /// <exception cref="Exception">串口通信异常时可能抛出异常</exception>
        protected async Task<string?> SendQueryAsync(string query)
        {
            if (!IsConnected)
            {
                App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 未连接，无法发送查询: {query}");
                return null;
            }

            await _communicationLock.WaitAsync();
            try
            {
                for (int attempt = 1; attempt <= _retryCount; attempt++)
                {
                    try
                    {
                        // 清空缓冲区
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();

                        // 发送查询命令
                        var queryWithTerminator = query + COMMAND_TERMINATOR;
                        _serialPort.Write(queryWithTerminator);

                        // 等待响应
                        await Task.Delay(RESPONSE_DELAY);

                        // 读取响应
                        var response = _serialPort.ReadLine().Trim();

                        App.AlarmService.Debug("SCPI设备", $"设备 {DeviceId} 查询成功: {query} -> {response} (尝试 {attempt}/{_retryCount})");

                        LastCommunicationTime = DateTime.Now;
                        return response;
                    }
                    catch (TimeoutException)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 查询超时 (尝试 {attempt}/{_retryCount}): {query}");
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 查询失败 (尝试 {attempt}/{_retryCount}): {query} - {ex.Message}");
                    }

                    if (attempt < _retryCount)
                    {
                        await Task.Delay(200); // 重试前等待更长时间
                    }
                }

                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 查询最终失败: {query}");
                return null;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 解析设备返回的数值响应
        /// 将设备响应字符串解析为双精度浮点数，明确区分解析失败和真实的0值
        /// </summary>
        /// <param name="response">设备响应字符串，可能为null或空白</param>
        /// <returns>解析成功返回对应的数值（包括0.0），解析失败返回null</returns>
        protected double? ParseNumericResponse(string? response)
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 响应为空或空白");
                return null;
            }

            if (double.TryParse(response.Trim(), out double value))
            {
                App.AlarmService.Debug("SCPI设备", $"设备 {DeviceId} 数值解析成功: {response} -> {value}");
                return value;
            }

            App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 数值响应解析失败: {response}");
            return null;
        }

        /// <summary>
        /// 解析设备返回的数值响应（兼容性方法）
        /// 为了向后兼容保留的方法，建议使用不带默认值的重载版本
        /// </summary>
        /// <param name="response">设备响应字符串，可能为null或空白</param>
        /// <param name="defaultValue">解析失败时返回的默认值</param>
        /// <returns>解析成功返回对应的数值，失败返回默认值</returns>
        [Obsolete("建议使用 ParseNumericResponse(string? response) 方法以明确区分解析失败和真实值")]
        protected double ParseNumericResponse(string? response, double defaultValue)
        {
            var result = ParseNumericResponse(response);
            return result ?? defaultValue;
        }

        /// <summary>
        /// 解析设备返回的布尔响应
        /// 将设备响应字符串解析为布尔值，支持"1"/"0"和"ON"/"OFF"格式
        /// </summary>
        /// <param name="response">设备响应字符串，可能为null或空白</param>
        /// <param name="defaultValue">解析失败时返回的默认值，默认为false</param>
        /// <returns>响应为"1"或"ON"（忽略大小写）时返回true，否则返回false或默认值</returns>
        protected bool ParseBooleanResponse(string? response, bool defaultValue = false)
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                return defaultValue;
            }

            var trimmed = response.Trim();
            return trimmed == "1" || trimmed.Equals("ON", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查设备连接状态
        /// 异步检查串口是否处于打开状态
        /// </summary>
        /// <returns>串口已打开返回true，否则返回false</returns>
        protected override async Task<bool> IsConnectedInternalAsync()
        {
            return await Task.FromResult(_serialPort?.IsOpen == true);
        }

        /// <summary>
        /// 释放非托管资源
        /// 关闭并释放串口连接，释放通信锁等资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _serialPort?.Close();
                _serialPort?.Dispose();
                _communicationLock?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
